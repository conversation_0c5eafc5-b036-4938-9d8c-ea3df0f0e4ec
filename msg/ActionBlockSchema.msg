# This message contains enums for different wall finishing levels
# and the goal enums for task planner

# Goal enums
uint8 CURRENT_JOB_AB=0
uint8 NAV_SUCCESS=1
uint8 NAV_CANCEL=2
uint8 NAV_ABORT=3
uint8 TOOLING_SUCCESS=4
uint8 TOOLING_CANCEL=5
uint8 TOOLING_ABORT=6
uint8 UNKNOWN_JOB_STATE=7
uint8 SET_TARGET_LEVEL=8
uint8 SET_TARGET_WALL=9
uint8 LOAD_TASK_CONFIG=10
uint8 LOAD_ZONE_MAP=11
uint8 ACTIVATE_TASK=12
uint8 START_TASK=13
uint8 PAUSE_TASK=14
uint8 RESET_TASK=15
uint8 SET_NORMAL_DIRECTION=16
uint8 SET_CORNER=17
uint8 UPDATE_COORDINATE_SYSTEM=18
uint8 GENERATE_AND_SAVE_ACTION_BLOCKS=19
uint8 SELECT_ACTION_BLOCK=20
uint8 DELETE_ACTION_BLOCKS=21
uint8 AUTO_CLEAN_MAP=22
uint8 AUTO_DETECT_WALLS=23
uint8 UPDATE_WALL=24
uint8 RESIZE_ACTION_BLOCK=25
uint8 UPDATE_ACTION_BLOCK_STATUS=26
uint8 SET_WALL_ORIENTATION=27
uint8 SEQUENTIAL_ZONE_PROCESSING=28

# level enums
uint8 L0=0
uint8 L1=1
uint8 L2=2
uint8 L3=3
uint8 L4=4
uint8 L5=5

# Tool type enums
uint8 SPRAYER=0
uint8 SANDER=1

# Normal direction enums
uint8 POSITIVE_NORMAL=0
uint8 NEGATIVE_NORMAL=1

# Corner enums
uint8 BOTTOM_LEFT=0
uint8 BOTTOM_RIGHT=1
uint8 TOP_LEFT=2
uint8 TOP_RIGHT=3

# Block type enums
uint8 AREA=0
uint8 LEFT_EDGE=1
uint8 RIGHT_EDGE=2
