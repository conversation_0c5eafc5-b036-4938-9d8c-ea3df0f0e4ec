# =============================================================================
# CAMERA CONFIGURATION PARAMETERS (config command - slow setup, done once)
# =============================================================================
float32 exposure_time              # Camera exposure time in microseconds (50.0 - 10000.0)
float32 gain                       # Camera gain in dB (0.0 - 40.0)
float32 acceleration_mm_per_s2     # Linear acceleration in mm/s² (1.0 - 500.0)
float32 scan_length                # Total scan length in mm
float32 working_distance           # Distance from lens to wall in mm
float32 overlap_percentage         # Image overlap percentage (0.0 - 100.0)

# =============================================================================
# CAPTURE EXECUTION PARAMETERS (start command - fast execution, repeatable)
# =============================================================================
float32 velocity_mm_per_s          # Linear velocity in mm/s (1.0 - 700.0)
uint64 reference_timestamp_ns       # Intended Timestamp for first image

# =============================================================================
# SYSTEM PARAMETERS (optional)
# =============================================================================
uint8 flash_duration_ms           # LED flash duration in milliseconds (1.0 - 100.0)
bool enable_flash                   # Enable/disable LED flash output
uint8 action_block_number         # Action sequence identifier for logging (1-10)
