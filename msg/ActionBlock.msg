
# Header message to get timestamp data of the goal
std_msgs/Header header

# Bounding box of the action block, check the expected format in the header file
vision_msgs/BoundingBox3D block
uint16 segmentation_resolution_x
uint16 segmentation_resolution_y

# Wall ID
# 1. Behaviour can send this as a request
# 2. UI Manager can set the wall target
# 3. In response task manager can populate the wall id
unique_identifier_msgs/UUID wall_id

uint8 level
uint8 tool

# Action block id for identification of action block
unique_identifier_msgs/UUID action_block_id

# block_type can be AREA, LEFT_EDGE or RIGHT_EDGE
uint8 block_type
