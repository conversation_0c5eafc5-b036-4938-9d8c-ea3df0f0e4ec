# Fault & severity enums
uint16 PLANNING_FAULT     = 0
uint16 MANIPULATION_FAULT = 1
uint16 PERCEPTION_FAULT   = 2
uint16 TASK_PLANNER_FAULT = 3
uint16 ACTUATOR_FAULT     = 4
uint16 SENSOR_FAULT       = 5
uint16 SYSTEM_FAULT       = 6

uint8  SEVERITY_WARN      = 0
uint8  SEVERITY_CRITICAL  = 1
uint8  SEVERITY_EMERGENCY = 2

# Error code enums
# Navigation error codes (0XX)
uint16 LOCALISATION_ERROR = 001
uint16 AMR_MOTOR_ERROR = 002
uint16 AUTO_INIT_ERROR = 003

# Manipulation error codes (1XX)
uint16 UR_ARM_ERROR = 101

# Actual payload
uint16 fault_code
uint16 error_code
bool   set               # true = currently active, false = cleared but still in snapshot
uint8  severity
KeyValue[] params
string message
builtin_interfaces/Time timestamp
