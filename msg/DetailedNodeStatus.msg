# Detailed node status message published by nodes via utility
string node_name       # Name of the node reporting status
builtin_interfaces/Time timestamp # Time the status was generated

# Reusing status constants from original NodeStatus for consistency
# Alternatively, define more granular states here if needed.
uint8 STATUS_OK=0
uint8 STATUS_WARN=1 # For non-critical issues
uint8 STATUS_ERROR=2
uint8 STATUS_INIT=3 # Initializing
uint8 STATUS_IDLE=4
uint8 STATUS_PROCESSING=5
# Add more specific states as needed

uint8 status          # Current status code (use constants above)

int32 error_code      # Optional error code (e.g., specific exception type or internal code)
                      # Use 0 if status is not ERROR/WARN

string message        # Detailed message (e.g., error description, status update)

